'use client';

/**
 * WCAG Reports Page
 * Page for generating and downloading WCAG compliance reports
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  ArrowLeft,
  Download,
  FileText,
  FileSpreadsheet,
  FileJson,
  Calendar,
  CheckCircle,
  AlertTriangle,
  Settings,
} from 'lucide-react';
import { useWcagState, useWcagActions } from '@/context/WcagContext';
import { WcagProvider } from '@/context/WcagContext';
import { WcagBreadcrumb } from '@/components/navigation/WcagBreadcrumb';

/**
 * WCAG Reports Content Component
 */
const WcagReportsContent: React.FC = () => {
  const router = useRouter();
  const state = useWcagState();
  const actions = useWcagActions();
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [mounted, setMounted] = useState(false);
  const [selectedScans, setSelectedScans] = useState<string[]>([]);
  const [exportFormat, setExportFormat] = useState<'pdf' | 'json' | 'csv'>('pdf');
  const [exportOptions, setExportOptions] = useState({
    includeEvidence: true,
    includeRecommendations: true,
    includeManualReviewItems: false,
  });

  // Handle client-side mounting
  // eslint-disable-next-line react-hooks/exhaustive-deps
  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => {
    // Implementation
  }, []);

  const loadCompletedScans = useCallback(async () => {
    try {
      await actions.fetchScans({ status: 'completed', limit: 50 });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error loading scans:', error);
    }
  }, [actions]);

  // Load completed scans
  useEffect(() => {
    loadCompletedScans();
  }, [loadCompletedScans]);

  const handleBack = () => {
    router.push('/dashboard/wcag');
  };

  const handleScanSelection = (scanId: string, checked: boolean) => {
    if (checked) {
      setSelectedScans([...selectedScans, scanId]);
    } else {
      setSelectedScans(selectedScans.filter((id) => id !== scanId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedScans(completedScans.map((scan) => scan.scanId));
    } else {
      setSelectedScans([]);
    }
  };

  const handleExportSelected = async () => {
    if (selectedScans.length === 0) {
      return;
    }

    try {
      // Export each selected scan
      for (const scanId of selectedScans) {
        await actions.exportScan(scanId, exportFormat, exportOptions);
        // Add small delay between exports to avoid overwhelming the server
        await new Promise((resolve) => setTimeout(resolve, 500));
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error exporting scans:', error);
    }
  };

  const handleExportSingle = async (scanId: string) => {
    try {
      await actions.exportScan(scanId, exportFormat, exportOptions);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error exporting scan:', error);
    }
  };

  const completedScans = state.scans.filter((scan) => scan.status === 'completed');

  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'pdf':
        return <FileText className="h-4 w-4" />;
      case 'csv':
        return <FileSpreadsheet className="h-4 w-4" />;
      case 'json':
        return <FileJson className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const getScoreBadge = (score: number) => {
    if (score >= 90) return <Badge variant="default">Excellent</Badge>;
    if (score >= 70) return <Badge variant="secondary">Good</Badge>;
    if (score >= 50) return <Badge variant="outline">Fair</Badge>;
    return <Badge variant="destructive">Poor</Badge>;
  };

  if (!mounted) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <WcagBreadcrumb />

      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" onClick={handleBack}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">WCAG Reports</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Generate and download compliance reports
          </p>
        </div>
      </div>

      {/* Error Alert */}
      {state.error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{state.error}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Export Configuration */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Export Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Format Selection */}
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
                Export Format
              </label>
              <Select
                value={exportFormat}
                onValueChange={(value: 'pdf' | 'json' | 'csv') => setExportFormat(value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pdf">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      PDF Report
                    </div>
                  </SelectItem>
                  <SelectItem value="json">
                    <div className="flex items-center gap-2">
                      <FileJson className="h-4 w-4" />
                      JSON Data
                    </div>
                  </SelectItem>
                  <SelectItem value="csv">
                    <div className="flex items-center gap-2">
                      <FileSpreadsheet className="h-4 w-4" />
                      CSV Spreadsheet
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Export Options */}
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
                Include in Export
              </label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="evidence"
                    checked={exportOptions.includeEvidence}
                    onCheckedChange={(checked) =>
                      setExportOptions((prev) => ({ ...prev, includeEvidence: checked as boolean }))
                    }
                  />
                  <label htmlFor="evidence" className="text-sm text-gray-600 dark:text-gray-400">
                    Evidence & Screenshots
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="recommendations"
                    checked={exportOptions.includeRecommendations}
                    onCheckedChange={(checked) =>
                      setExportOptions((prev) => ({
                        ...prev,
                        includeRecommendations: checked as boolean,
                      }))
                    }
                  />
                  <label
                    htmlFor="recommendations"
                    className="text-sm text-gray-600 dark:text-gray-400"
                  >
                    Recommendations
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="manual"
                    checked={exportOptions.includeManualReviewItems}
                    onCheckedChange={(checked) =>
                      setExportOptions((prev) => ({
                        ...prev,
                        includeManualReviewItems: checked as boolean,
                      }))
                    }
                  />
                  <label htmlFor="manual" className="text-sm text-gray-600 dark:text-gray-400">
                    Manual Review Items
                  </label>
                </div>
              </div>
            </div>

            {/* Bulk Export */}
            <div className="pt-4 border-t">
              <Button
                onClick={handleExportSelected}
                disabled={selectedScans.length === 0 || state.loading.exporting}
                className="w-full"
              >
                {getFormatIcon(exportFormat)}
                <span className="ml-2">Export Selected ({selectedScans.length})</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Scans List */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Completed Scans ({completedScans.length})</span>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="select-all"
                  checked={
                    selectedScans.length === completedScans.length && completedScans.length > 0
                  }
                  onCheckedChange={handleSelectAll}
                />
                <label htmlFor="select-all" className="text-sm text-gray-600 dark:text-gray-400">
                  Select All
                </label>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {state.loading.fetching ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
              </div>
            ) : completedScans.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 dark:text-gray-400">
                  No completed scans available for export.
                </p>
                <Button className="mt-4" onClick={() => router.push('/dashboard/wcag/scan')}>
                  Start First Scan
                </Button>
              </div>
            ) : (
              <div className="space-y-3">
                {completedScans.map((scan) => (
                  <div
                    key={scan.scanId}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                  >
                    <div className="flex items-center gap-4 flex-1">
                      <Checkbox
                        checked={selectedScans.includes(scan.scanId)}
                        onCheckedChange={(checked) =>
                          handleScanSelection(scan.scanId, checked as boolean)
                        }
                      />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                          <p className="font-medium text-gray-900 dark:text-white truncate">
                            {scan.url}
                          </p>
                        </div>
                        <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                          <span className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {new Date(scan.scanTimestamp).toLocaleDateString()}
                          </span>
                          <span>WCAG {scan.wcagVersion}</span>
                          <span>Level {scan.complianceLevel}</span>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-gray-900 dark:text-white">
                          {scan.overallScore ? Number(scan.overallScore).toFixed(1) : '0.0'}%
                        </p>
                        {getScoreBadge(Number(scan.overallScore) || 0)}
                      </div>
                    </div>
                    <div className="ml-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleExportSingle(scan.scanId)}
                        disabled={state.loading.exporting}
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Export Formats Info */}
      <Card>
        <CardHeader>
          <CardTitle>Export Format Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <div className="flex items-center gap-2 mb-2">
                <FileText className="h-5 w-5 text-red-500" />
                <h4 className="font-semibold text-gray-900 dark:text-white">PDF Report</h4>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Comprehensive formatted report with visual elements, charts, and detailed findings.
                Perfect for stakeholder presentations and compliance documentation.
              </p>
            </div>
            <div>
              <div className="flex items-center gap-2 mb-2">
                <FileJson className="h-5 w-5 text-blue-500" />
                <h4 className="font-semibold text-gray-900 dark:text-white">JSON Data</h4>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Raw structured data format ideal for integration with other systems, custom
                reporting tools, or programmatic analysis.
              </p>
            </div>
            <div>
              <div className="flex items-center gap-2 mb-2">
                <FileSpreadsheet className="h-5 w-5 text-green-500" />
                <h4 className="font-semibold text-gray-900 dark:text-white">CSV Spreadsheet</h4>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Tabular data format for analysis in Excel, Google Sheets, or other spreadsheet
                applications. Great for tracking and trending.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

/**
 * Main WCAG Reports Page with Provider
 */
export default function WcagReportsPage() {
  return (
    <WcagProvider>
      <WcagReportsContent />
    </WcagProvider>
  );
}
