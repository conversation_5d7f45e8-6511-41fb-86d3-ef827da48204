/**
 * WCAG API Routes
 * RESTful endpoints for WCAG compliance scanning
 */

import { Router, Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import {
  authenticateWcagRequest,
  validateRequest,
  validateQuery,
  wcagRateLimit,
  wcagSecurityHeaders,
  wcagErrorHandler,
} from './middleware';
import {
  WcagScanRequestSchema,
  WcagScanListQuerySchema,
  WcagExportRequestSchema,
  type WcagScanRequest,
  type WcagScanListQuery,
  type WcagExportRequest,
} from './schemas';
import { type WcagScanResult, type WcagCheckResult, type WcagEvidence } from '../types';
import { WcagDatabase } from '../database/wcag-database';
import { WcagOrchestrator, type WcagScanConfig } from '../orchestrator';
import logger from '../../../utils/logger';

const router = Router();
const wcagDatabase = new WcagDatabase();
const wcagOrchestrator = new WcagOrchestrator();

// Apply security middleware to all WCAG routes
router.use(wcagSecurityHeaders);
router.use(wcagRateLimit);

// Development-friendly authentication middleware
router.use((req, res, next) => {
  const isDevelopment = process.env.NODE_ENV === 'development';

  if (isDevelopment) {
    // In development, provide mock authentication
    req.requestId = uuidv4();
    req.user = {
      id: '9fed30a7-64b4-4ffe-b531-6e9cf592952b', // Use existing user UUID from database
      email: '<EMAIL>',
      roles: ['user', 'admin'],
      permissions: ['wcag:scan', 'wcag:view', 'wcag:export'],
    };
    logger.info(`🔓 [${req.requestId}] Development mode: Mock authentication applied`);
    next();
  } else {
    // In production, use proper Keycloak authentication
    authenticateWcagRequest(req, res, next);
  }
});

/**
 * POST /api/v1/compliance/wcag/scan
 * Initiate a new WCAG compliance scan
 */
router.post(
  '/scan',
  validateRequest(WcagScanRequestSchema),
  async (req: Request, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = req.requestId!;

    try {
      logger.info(`🚀 [${requestId}] WCAG scan request received`);
      logger.info(`📋 [${requestId}] Request headers:`, {
        'content-type': req.headers['content-type'],
        'user-agent': req.headers['user-agent'],
        authorization: req.headers.authorization ? 'Bearer [REDACTED]' : 'None',
      });
      logger.info(`👤 [${requestId}] User context:`, {
        userId: req.user?.id,
        email: req.user?.email,
        permissions: req.user?.permissions,
      });

      const scanRequest: WcagScanRequest = req.body;
      const userId = req.user!.id;

      logger.info(`🎯 [${requestId}] Target URL: ${scanRequest.targetUrl}`);
      logger.info(`⚙️ [${requestId}] Scan options:`, scanRequest.scanOptions);

      // Validate target URL
      try {
        new URL(scanRequest.targetUrl);
        logger.info(`✅ [${requestId}] Target URL validation passed`);
      } catch (urlError) {
        logger.error(`❌ [${requestId}] Invalid target URL: ${scanRequest.targetUrl}`);
        throw new Error(`Invalid target URL: ${scanRequest.targetUrl}`);
      }

      // Create scan configuration for orchestrator
      const orchestratorConfig: WcagScanConfig = {
        targetUrl: scanRequest.targetUrl,
        timeout: scanRequest.scanOptions?.timeout || 30000,
        wcagVersion: scanRequest.scanOptions?.wcagVersion === 'all' ? '2.2' : (scanRequest.scanOptions?.wcagVersion as '2.0' | '2.1' | '2.2' | '3.0') || '2.2',
        level: scanRequest.scanOptions?.level || 'AA',
        enableManualReview: false, // Automated scan only for now
        maxConcurrentChecks: 3,
        userId,
      };

      logger.info(`🔧 [${requestId}] Scan configuration created:`, {
        targetUrl: orchestratorConfig.targetUrl,
        userId: orchestratorConfig.userId,
        wcagVersion: orchestratorConfig.wcagVersion,
        level: orchestratorConfig.level,
        timeout: orchestratorConfig.timeout,
      });

      // Generate scan ID for immediate response
      const scanId = uuidv4();

      // Add scanId to orchestrator config
      orchestratorConfig.scanId = scanId;

      // Prepare response with scan ID and pending status
      const scanResult = {
        scanId,
        targetUrl: orchestratorConfig.targetUrl,
        status: 'pending' as const,
        metadata: {
          scanId,
          userId,
          requestId,
          startTime: new Date().toISOString(),
          userAgent: req.headers['user-agent'] || 'Unknown',
          viewport: { width: 1920, height: 1080 },
          environment: process.env.NODE_ENV || 'development',
          version: '1.0.0',
          wcagVersion: orchestratorConfig.wcagVersion,
          level: orchestratorConfig.level,
        },
      };

      // Start the actual WCAG scan asynchronously (don't await)
      logger.info(`🚀 [${requestId}] Starting comprehensive WCAG scan with orchestrator`);

      // Start scan in background - don't await to return immediately
      wcagOrchestrator.performComprehensiveScan(orchestratorConfig)
        .then((completedScanId) => {
          logger.info(`✅ [${requestId}] WCAG scan completed successfully: ${completedScanId}`);
        })
        .catch((error) => {
          logger.error(`❌ [${requestId}] WCAG scan failed:`, { error: error.message, scanId });
        });

      logger.info(`🆔 [${requestId}] Scan initiated with ID: ${scanId}`);

      logger.info(`📊 [${requestId}] Scan started successfully:`, {
        scanId: scanResult.scanId,
        status: scanResult.status,
        targetUrl: scanResult.targetUrl,
      });

      const processingTime = Date.now() - startTime;
      logger.info(`✅ [${requestId}] WCAG scan initiated successfully in ${processingTime}ms`);

      res.status(200).json({
        success: true,
        data: scanResult,
        requestId,
        processingTime,
      });
    } catch (error) {
      const processingTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;

      logger.error(`❌ [${requestId}] WCAG scan failed:`, {
        error: errorMessage,
        stack: errorStack,
        processingTime,
        requestBody: req.body,
      });

      res.status(500).json({
        success: false,
        error: {
          code: 'WCAG_SCAN_ERROR',
          message: 'Failed to complete WCAG compliance scan',
          details:
            process.env.NODE_ENV === 'development'
              ? {
                  error: errorMessage,
                  stack: errorStack,
                  requestBody: req.body,
                }
              : undefined,
          context: 'An error occurred during the WCAG scanning process',
        },
        requestId,
        processingTime,
      });
    }
  },
);

/**
 * GET /api/v1/compliance/wcag/scans
 * Get list of user's WCAG scans with pagination
 */
router.get(
  '/scans',
  validateQuery(WcagScanListQuerySchema),
  async (req: Request, res: Response): Promise<void> => {
    const startTime = Date.now();

    try {
      const query: WcagScanListQuery = req.query as unknown as WcagScanListQuery;
      const userId = req.user!.id;
      const requestId = req.requestId!;

      logger.info(`📋 [${requestId}] Fetching WCAG scans for user: ${userId}`);

      // Get scans from database
      const scansResult = await wcagDatabase.getUserScans(userId, {
        page: query.page,
        limit: query.limit,
        status: query.status,
        startDate: query.startDate ? new Date(query.startDate) : undefined,
        endDate: query.endDate ? new Date(query.endDate) : undefined,
        sortBy: query.sortBy,
        sortOrder: query.sortOrder,
      });

      const processingTime = Date.now() - startTime;

      res.status(200).json({
        success: true,
        data: scansResult,
        requestId,
        processingTime,
      });
    } catch (error) {
      const processingTime = Date.now() - startTime;
      logger.error(`❌ [${req.requestId}] Failed to fetch WCAG scans`, { error });

      res.status(500).json({
        success: false,
        error: {
          code: 'WCAG_DATABASE_ERROR',
          message: 'Failed to retrieve WCAG scans',
          context: 'Unable to fetch scan history from database',
        },
        requestId: req.requestId!,
        processingTime,
      });
    }
  },
);

/**
 * GET /api/v1/compliance/wcag/scans/:scanId
 * Get detailed results for a specific WCAG scan
 */
router.get('/scans/:scanId', async (req: Request, res: Response): Promise<void> => {
  const startTime = Date.now();

  try {
    const { scanId } = req.params;
    const userId = req.user!.id;
    const requestId = req.requestId!;

    // Validate scanId format
    if (!scanId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
      res.status(400).json({
        success: false,
        error: {
          code: 'WCAG_VALIDATION_ERROR',
          message: 'Invalid scan ID format',
          context: 'Scan ID must be a valid UUID',
        },
        requestId,
        processingTime: Date.now() - startTime,
      });
      return;
    }

    logger.info(`🔍 [${requestId}] Fetching WCAG scan details: ${scanId}`);

    // Get scan details from database
    const scanResult = await wcagDatabase.getScanById(scanId, userId);

    if (!scanResult) {
      res.status(404).json({
        success: false,
        error: {
          code: 'WCAG_SCAN_NOT_FOUND',
          message: 'WCAG scan not found',
          context: 'The requested scan does not exist or you do not have access to it',
        },
        requestId,
        processingTime: Date.now() - startTime,
      });
      return;
    }

    const processingTime = Date.now() - startTime;

    res.status(200).json({
      success: true,
      data: scanResult,
      requestId,
      processingTime,
    });
  } catch (error) {
    const processingTime = Date.now() - startTime;
    logger.error(`❌ [${req.requestId}] Failed to fetch WCAG scan details`, { error });

    res.status(500).json({
      success: false,
      error: {
        code: 'WCAG_DATABASE_ERROR',
        message: 'Failed to retrieve WCAG scan details',
        context: 'Unable to fetch scan details from database',
      },
      requestId: req.requestId!,
      processingTime,
    });
  }
});

/**
 * DELETE /api/v1/compliance/wcag/scans/:scanId
 * Delete a specific WCAG scan
 */
router.delete('/scans/:scanId', async (req: Request, res: Response): Promise<void> => {
  const startTime = Date.now();

  try {
    const { scanId } = req.params;
    const userId = req.user!.id;
    const requestId = req.requestId!;

    // Validate scanId format
    if (!scanId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
      res.status(400).json({
        success: false,
        error: {
          code: 'WCAG_VALIDATION_ERROR',
          message: 'Invalid scan ID format',
          context: 'Scan ID must be a valid UUID',
        },
        requestId,
        processingTime: Date.now() - startTime,
      });
      return;
    }

    logger.info(`🗑️ [${requestId}] Deleting WCAG scan: ${scanId}`);

    // Delete scan from database
    const deleted = await wcagDatabase.deleteScan(scanId, userId);

    if (!deleted) {
      res.status(404).json({
        success: false,
        error: {
          code: 'WCAG_SCAN_NOT_FOUND',
          message: 'WCAG scan not found',
          context: 'The requested scan does not exist or you do not have access to it',
        },
        requestId,
        processingTime: Date.now() - startTime,
      });
      return;
    }

    const processingTime = Date.now() - startTime;

    res.status(200).json({
      success: true,
      data: { message: 'WCAG scan deleted successfully' },
      requestId,
      processingTime,
    });
  } catch (error) {
    const processingTime = Date.now() - startTime;
    logger.error(`❌ [${req.requestId}] Failed to delete WCAG scan`, { error });

    res.status(500).json({
      success: false,
      error: {
        code: 'WCAG_DATABASE_ERROR',
        message: 'Failed to delete WCAG scan',
        context: 'Unable to delete scan from database',
      },
      requestId: req.requestId!,
      processingTime,
    });
  }
});

/**
 * POST /api/v1/compliance/wcag/export
 * Export WCAG scan results in various formats
 */
router.post(
  '/export',
  validateRequest(WcagExportRequestSchema),
  async (req: Request, res: Response): Promise<void> => {
    const startTime = Date.now();

    try {
      const exportRequest: WcagExportRequest = req.body;
      const userId = req.user!.id;
      const requestId = req.requestId!;

      logger.info(
        `📄 [${requestId}] Exporting WCAG scan: ${exportRequest.scanId} as ${exportRequest.format}`,
      );

      // Check if user has export permissions
      if (!req.user!.permissions.includes('wcag:export')) {
        res.status(403).json({
          success: false,
          error: {
            code: 'WCAG_AUTHORIZATION_ERROR',
            message: 'Insufficient permissions for WCAG export',
            context: 'User lacks required export permissions',
          },
          requestId,
          processingTime: Date.now() - startTime,
        });
        return;
      }

      // Get scan data
      const scanResult = await wcagDatabase.getScanById(exportRequest.scanId, userId);

      if (!scanResult) {
        res.status(404).json({
          success: false,
          error: {
            code: 'WCAG_SCAN_NOT_FOUND',
            message: 'WCAG scan not found for export',
            context: 'The requested scan does not exist or you do not have access to it',
          },
          requestId,
          processingTime: Date.now() - startTime,
        });
        return;
      }

      // Generate export (implementation depends on format)
      const exportResult = await generateWcagExport(scanResult, exportRequest);

      const processingTime = Date.now() - startTime;
      logger.info(`📄 Export generated in ${processingTime}ms`);

      // Set appropriate headers based on format
      if (exportRequest.format === 'pdf') {
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader(
          'Content-Disposition',
          `attachment; filename="wcag-report-${exportRequest.scanId}.pdf"`,
        );
      } else if (exportRequest.format === 'json') {
        res.setHeader('Content-Type', 'application/json');
        res.setHeader(
          'Content-Disposition',
          `attachment; filename="wcag-report-${exportRequest.scanId}.json"`,
        );
      } else if (exportRequest.format === 'csv') {
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader(
          'Content-Disposition',
          `attachment; filename="wcag-report-${exportRequest.scanId}.csv"`,
        );
      }

      res.status(200).send(exportResult);
    } catch (error) {
      const processingTime = Date.now() - startTime;
      logger.error(`❌ [${req.requestId}] Failed to export WCAG scan`, { error });

      res.status(500).json({
        success: false,
        error: {
          code: 'WCAG_EXPORT_ERROR',
          message: 'Failed to export WCAG scan results',
          context: 'Unable to generate export file',
        },
        requestId: req.requestId!,
        processingTime,
      });
    }
  },
);

/**
 * GET /api/v1/compliance/wcag/health
 * Health check for WCAG API
 */
router.get('/health', async (req: Request, res: Response): Promise<void> => {
  const startTime = Date.now();

  try {
    const requestId = req.requestId!;

    logger.info(`🏥 [${requestId}] WCAG API health check initiated`);
    logger.info(`📋 [${requestId}] Environment: ${process.env.NODE_ENV || 'development'}`);

    // Check database connectivity
    logger.info(`🔍 [${requestId}] Checking database connectivity...`);
    const dbHealthy = await wcagDatabase.healthCheck();
    logger.info(`💾 [${requestId}] Database health: ${dbHealthy ? 'HEALTHY' : 'UNHEALTHY'}`);

    const processingTime = Date.now() - startTime;

    if (dbHealthy) {
      logger.info(`✅ [${requestId}] WCAG API health check passed in ${processingTime}ms`);
      res.status(200).json({
        success: true,
        data: {
          status: 'healthy',
          timestamp: new Date().toISOString(),
          version: '1.0.0',
          database: 'connected',
          environment: process.env.NODE_ENV || 'development',
        },
        requestId,
        processingTime,
      });
    } else {
      logger.warn(`⚠️ [${requestId}] WCAG API health check failed - database unhealthy`);
      res.status(503).json({
        success: false,
        error: {
          code: 'WCAG_SERVICE_UNAVAILABLE',
          message: 'WCAG API service is unhealthy',
          context: 'Database connectivity issues detected',
        },
        requestId,
        processingTime,
      });
    }
  } catch (error) {
    const processingTime = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : String(error);

    logger.error(`❌ [${req.requestId}] WCAG health check failed:`, {
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined,
      processingTime,
    });

    res.status(503).json({
      success: false,
      error: {
        code: 'WCAG_SERVICE_UNAVAILABLE',
        message: 'WCAG API service is unhealthy',
        context: 'Health check failed',
        details: process.env.NODE_ENV === 'development' ? { error: errorMessage } : undefined,
      },
      requestId: req.requestId!,
      processingTime,
    });
  }
});

// Apply error handling middleware
router.use(wcagErrorHandler);

/**
 * Enhanced generateWcagExport function
 * Implements comprehensive export functionality following established patterns
 */
async function generateWcagExport(
  scanResult: WcagScanResult,
  exportRequest: WcagExportRequest,
): Promise<Buffer | string> {
  logger.info(
    `📄 Generating ${exportRequest.format.toUpperCase()} export for scan: ${scanResult.scanId}`,
  );

  switch (exportRequest.format) {
    case 'pdf':
      return generatePdfReport(scanResult, exportRequest);
    case 'json':
      return generateJsonExport(scanResult, exportRequest);
    case 'csv':
      return generateCsvExport(scanResult, exportRequest);
    default:
      throw new Error(`Unsupported export format: ${exportRequest.format}`);
  }
}

/**
 * Generate PDF report using comprehensive text-based approach
 * Following existing patterns without external PDF libraries
 */
async function generatePdfReport(
  scanResult: WcagScanResult,
  exportRequest: WcagExportRequest,
): Promise<Buffer> {
  // Generate comprehensive text report that can be converted to PDF
  const reportContent = buildTextReport(scanResult, exportRequest);

  // Return as text buffer - can be enhanced with PDF library later
  const buffer = Buffer.from(reportContent, 'utf8');

  return buffer;
}

/**
 * Build comprehensive text report content
 * Following existing WCAG data structure patterns
 */
function buildTextReport(scanResult: WcagScanResult, exportRequest: WcagExportRequest): string {
  const sections: string[] = [];

  // Title Page
  sections.push(buildTitleSection(scanResult));

  // Executive Summary
  sections.push(buildExecutiveSummary(scanResult));

  // Detailed Results
  sections.push(buildDetailedResults(scanResult, exportRequest));

  // Recommendations
  if (exportRequest.includeRecommendations) {
    sections.push(buildRecommendationsSection(scanResult));
  }

  // Manual Review Items - separate tracking
  if (exportRequest.includeManualReviewItems) {
    sections.push(buildManualReviewSection(scanResult));
  }

  // Appendix
  sections.push(buildAppendixSection(scanResult));

  return sections.join('\n\n' + '='.repeat(80) + '\n\n');
}

/**
 * Build title section
 * Using actual WCAG data structure
 */
function buildTitleSection(scanResult: WcagScanResult): string {
  const lines = [
    'WCAG COMPLIANCE REPORT',
    '='.repeat(50),
    '',
    `Target URL: ${scanResult.targetUrl}`,
    `Scan Date: ${new Date(scanResult.metadata.startTime).toLocaleDateString()}`,
    `Overall Score: ${scanResult.overallScore}/100`,
    `Level Achieved: ${scanResult.levelAchieved}`,
    `Risk Level: ${scanResult.riskLevel.toUpperCase()}`,
    `Scan ID: ${scanResult.scanId}`,
  ];

  return lines.join('\n');
}

/**
 * Build executive summary
 * Using correct WCAG data structure properties
 */
function buildExecutiveSummary(scanResult: WcagScanResult): string {
  const lines = [
    'EXECUTIVE SUMMARY',
    '='.repeat(50),
    '',
    `This report presents the results of a comprehensive WCAG compliance analysis conducted on ${new Date(scanResult.metadata.startTime).toLocaleDateString()}.`,
    '',
    'KEY FINDINGS:',
    '-'.repeat(20),
    `• Overall Compliance Score: ${scanResult.overallScore}/100`,
    `• WCAG Level Achieved: ${scanResult.levelAchieved}`,
    `• Total Automated Checks: ${scanResult.summary.totalAutomatedChecks}`,
    `• Passed Automated Checks: ${scanResult.summary.passedAutomatedChecks}`,
    `• Failed Automated Checks: ${scanResult.summary.failedAutomatedChecks}`,
    `• Risk Level: ${scanResult.riskLevel.toUpperCase()}`,
    '',
    'CATEGORY SCORES:',
    '-'.repeat(20),
  ];

  // Add category scores using correct property structure
  Object.entries(scanResult.summary.categoryScores).forEach(([category, score]) => {
    lines.push(`• ${category.charAt(0).toUpperCase() + category.slice(1)}: ${score}/100`);
  });

  return lines.join('\n');
}

/**
 * Build detailed results section
 * Using correct WCAG check result structure
 */
function buildDetailedResults(
  scanResult: WcagScanResult,
  exportRequest: WcagExportRequest,
): string {
  const lines = ['DETAILED RESULTS', '='.repeat(50), ''];

  // Group checks by category
  const checksByCategory = scanResult.checks.reduce(
    (acc, check) => {
      if (!acc[check.category]) acc[check.category] = [];
      acc[check.category].push(check);
      return acc;
    },
    {} as Record<string, WcagCheckResult[]>,
  );

  Object.entries(checksByCategory).forEach(([category, checks]) => {
    lines.push(`${category.charAt(0).toUpperCase() + category.slice(1)} (${checks.length} checks)`);
    lines.push('-'.repeat(40));
    lines.push('');

    checks.forEach((check, index) => {
      lines.push(`${index + 1}. ${check.ruleName} (${check.ruleId})`);
      lines.push(`   Status: ${check.status.toUpperCase()}`);
      lines.push(`   Score: ${check.score}/${check.maxScore}`);
      lines.push(`   Level: ${check.level}`);
      lines.push(`   WCAG Version: ${check.wcagVersion}`);
      lines.push(`   Automated: ${check.automated ? 'Yes' : 'No'}`);

      if (exportRequest.includeEvidence && check.evidence.length > 0) {
        lines.push('   Evidence:');
        check.evidence.slice(0, 3).forEach((evidence: WcagEvidence) => {
          lines.push(`     • ${evidence.description}: ${evidence.value}`);
        });
      }

      if (check.errorMessage) {
        lines.push(`   Error: ${check.errorMessage}`);
      }

      lines.push('');
    });

    lines.push('');
  });

  return lines.join('\n');
}

/**
 * Build recommendations section
 */
function buildRecommendationsSection(scanResult: WcagScanResult): string {
  const lines = ['RECOMMENDATIONS', '='.repeat(50), ''];

  if (scanResult.recommendations.length === 0) {
    lines.push('No specific recommendations generated for this scan.');
    return lines.join('\n');
  }

  scanResult.recommendations.forEach((rec, index) => {
    lines.push(`${index + 1}. ${rec.title}`);
    lines.push(`   Priority: ${rec.priority.toUpperCase()}`);
    lines.push(`   Category: ${rec.category}`);
    lines.push(`   Description: ${rec.description}`);
    lines.push(`   Implementation: ${rec.implementation}`);

    if (rec.resources && rec.resources.length > 0) {
      lines.push('   Resources:');
      rec.resources.forEach((resource) => {
        lines.push(`     • ${resource}`);
      });
    }

    lines.push('');
  });

  return lines.join('\n');
}

/**
 * Build manual review section - separate tracking
 * Note: Manual review items are tracked separately from automated checks
 */
function buildManualReviewSection(_scanResult: WcagScanResult): string {
  const lines = [
    'MANUAL REVIEW ITEMS',
    '='.repeat(50),
    '',
    'The following items require manual review to complete the accessibility assessment:',
    '',
  ];

  // Manual review items would be fetched separately from the database
  // For now, we'll note that they exist but are tracked separately
  lines.push('Manual review items are tracked separately from automated checks.');
  lines.push('These items require human evaluation and are not included in the automated score.');
  lines.push('');
  lines.push('To view manual review items, use the dedicated manual review dashboard.');

  return lines.join('\n');
}

/**
 * Build appendix section
 */
function buildAppendixSection(scanResult: WcagScanResult): string {
  const lines = [
    'APPENDIX',
    '='.repeat(50),
    '',
    'SCAN METADATA:',
    '-'.repeat(20),
    `Scan ID: ${scanResult.scanId}`,
    `User Agent: ${scanResult.metadata.userAgent}`,
    `Viewport: ${scanResult.metadata.viewport.width}x${scanResult.metadata.viewport.height}`,
    `Environment: ${scanResult.metadata.environment}`,
    `Version: ${scanResult.metadata.version}`,
  ];

  if (scanResult.metadata.duration) {
    lines.push(`Duration: ${Math.round(scanResult.metadata.duration / 1000)}s`);
  }

  lines.push('');
  lines.push('ABOUT THIS REPORT:');
  lines.push('-'.repeat(20));
  lines.push('This report was generated by an automated WCAG compliance scanner that achieves');
  lines.push('87% automation across 21 WCAG rules. The scanner follows WCAG 2.1, 2.2, and 3.0');
  lines.push('guidelines to provide comprehensive accessibility analysis.');
  lines.push('');
  lines.push('For questions about this report or to schedule manual accessibility testing,');
  lines.push('please contact your accessibility team.');

  return lines.join('\n');
}

/**
 * Generate JSON export
 * Following existing GDPR export patterns
 */
async function generateJsonExport(
  scanResult: WcagScanResult,
  exportRequest: WcagExportRequest,
): Promise<Buffer> {
  // Create filtered result based on options
  const exportData = {
    scanResult: {
      ...scanResult,
      checks: exportRequest.includeEvidence
        ? scanResult.checks
        : scanResult.checks.map((check) => ({ ...check, evidence: [] })),
      recommendations: exportRequest.includeRecommendations ? scanResult.recommendations : [],
    },
    exportMetadata: {
      exportDate: new Date().toISOString(),
      exportOptions: exportRequest,
      version: '1.0.0',
    },
  };

  const jsonString = JSON.stringify(exportData, null, 2);
  return Buffer.from(jsonString, 'utf8');
}

/**
 * Generate CSV export
 * Following existing GDPR CSV export patterns (in-memory processing)
 */
async function generateCsvExport(
  scanResult: WcagScanResult,
  _exportRequest: WcagExportRequest,
): Promise<Buffer> {
  // CSV headers
  const headers = [
    'Rule ID',
    'Rule Name',
    'Category',
    'WCAG Version',
    'Level',
    'Status',
    'Score',
    'Max Score',
    'Automated',
    'Execution Time (ms)',
    'Evidence Count',
    'Error Message',
  ];

  // CSV rows
  const rows = scanResult.checks.map((check) => [
    check.ruleId,
    `"${check.ruleName}"`,
    check.category,
    check.wcagVersion,
    check.level,
    check.status,
    check.score.toString(),
    check.maxScore.toString(),
    check.automated.toString(),
    check.executionTime.toString(),
    check.evidence.length.toString(),
    `"${check.errorMessage || ''}"`,
  ]);

  // Build CSV content
  const csvContent = [headers.join(','), ...rows.map((row) => row.join(','))].join('\n');

  return Buffer.from(csvContent, 'utf8');
}

export default router;
